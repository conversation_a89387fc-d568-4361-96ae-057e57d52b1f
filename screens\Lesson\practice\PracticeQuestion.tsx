import { <PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useState } from "react";
import XSvg from "../../../assets/svg/xSvg";
import ProgressBar from "../../../components/ui/ProgressBar";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import QuestionContent from "./QuestionContent";
import { Question, Result } from "../../../types/module";

export default function PracticeQuestion({
   onFinishPart,
   questions: _questions,
}: {
   onFinishPart: (result: Result) => void;
   questions: Question[];
}) {
   const { navigate } = useNavigation();

   useFocusEffect(
      React.useCallback(() => {
         const onBackPress = () => {
            setModalVisible(true);
            return true;
         };

         BackHandler.addEventListener("hardwareBackPress", onBackPress);

         return () => BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      }, [])
   );

   const [modalVisible, setModalVisible] = useState(false);

   const [questions, setQuestions] = useState(_questions);
   const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
   const [progress, setProgress] = useState(0);
   const [mistakensQIds, setMistakenQIds] = useState<number[]>([]);

   const questionsCount = _questions.length;

   const handleNextQuestion = () => {
      if (currentQuestionIndex == questionsCount - 1) {
         const result: Result = {
            user_id: -1,
            module_id: -1,
            mistaken_questions_ids: mistakensQIds,
            time: "-",
         };
         onFinishPart(result);
         // @ts-ignore
         navigate("Lesson");
         return;
      }

      setCurrentQuestionIndex((prev) => prev + 1);
   };
   const handleSaveResult = (isCorrect: boolean) => {
      if (isCorrect) {
         setProgress(progress + 100 / _questions.length);
      }

      if (!isCorrect) {
         const incorrectQuestion = questions[currentQuestionIndex];
         setQuestions([...questions, incorrectQuestion]);

         setMistakenQIds([...mistakensQIds, questions[currentQuestionIndex].id]);
      }
   };
   return (
      <View>
         <View
            style={{
               marginTop: 25,
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               width: "90%",
               marginHorizontal: "auto",
               alignItems: "center",
            }}
         >
            <TouchableOpacity
               onPress={() => {
                  setModalVisible(true);
               }}
            >
               <XSvg />
            </TouchableOpacity>
            <ProgressBar height={16} progress={progress} />
         </View>

         {/* <QuestionContent
                  onNextQuestion={handleNextQuestion}
                  onSaveResult={handleSaveResult}
                  question={mockQuestions[currentQuestionIndex]}
               /> */}

         <QuestionContent
            onNextQuestion={handleNextQuestion}
            onSaveResult={handleSaveResult}
            question={questions[currentQuestionIndex]}
         />

         <Modal
            animationType="fade"
            statusBarTranslucent={true}
            transparent={true}
            presentationStyle="overFullScreen"
            visible={modalVisible}
            onRequestClose={() => {
               setModalVisible(false);
            }}
         >
            <View style={styles.modalContainer}>
               <View style={styles.modalContent}>
                  <Text style={styles.modalText}>Ваш прогресс не сохранится. Уверены что хотите покинуть урок?</Text>
                  <View style={styles.modalButtons}>
                     <TouchableOpacity style={styles.button} onPress={() => setModalVisible(false)}>
                        <Text style={styles.buttonText}>Нет</Text>
                     </TouchableOpacity>
                     <TouchableOpacity
                        style={styles.button}
                        onPress={() => {
                           setModalVisible(false);
                           // @ts-ignore
                           navigate("Home");
                        }}
                     >
                        <Text style={styles.buttonText}>Да</Text>
                     </TouchableOpacity>
                  </View>
               </View>
            </View>
         </Modal>
      </View>
   );
}

const styles = StyleSheet.create({
   modalContainer: {
      flex: 1,
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.5)",
   },
   modalContent: {
      margin: 20,
      backgroundColor: "white",
      borderRadius: 25,
      padding: 35,
      alignItems: "center",
   },
   modalText: {
      marginBottom: 15,
      textAlign: "center",
      fontSize: 16,
      color: "#333",
   },
   modalButtons: {
      flexDirection: "row",
   },
   button: {
      flex: 1,
      marginHorizontal: 5,
      paddingVertical: 10,
      borderRadius: 5,
      backgroundColor: "#F1C644",
      alignItems: "center",
   },
   buttonText: {
      color: "#fff",
      fontWeight: "bold",
   },
});
