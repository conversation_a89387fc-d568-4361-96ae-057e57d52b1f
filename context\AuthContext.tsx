import React, { createContext, useState, useEffect, useContext, ReactNode } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { User, Role } from "../types/auth";
import { setupInterceptors, baseUrl } from "../utils/service";

interface AuthState {
   token: string | null;
   authenticated: boolean;
   role: Role | null;
   user: User | null;
   refreshToken: string | null;
}

interface AuthContextType {
   authState: AuthState;
   login: (access: string, refresh: string, userData: User) => Promise<void>;
   logout: () => Promise<void>;
   refresh: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
   const context = useContext(AuthContext);
   if (!context) {
      throw new Error("useAuth must be used within an AuthProvider");
   }
   return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
   const [authState, setAuthState] = useState<AuthState>({
      token: null,
      authenticated: false,
      role: null,
      user: null,
      refreshToken: null,
   });

   useEffect(() => {
      const loadAuthData = async () => {
         const storedAccessToken = await AsyncStorage.getItem("accessToken");
         const storedRefreshToken = await AsyncStorage.getItem("refreshToken");
         const storedUser = await AsyncStorage.getItem("user");

         if (storedAccessToken && storedRefreshToken && storedUser) {
            const parsedUser = JSON.parse(storedUser) as User;
            setAuthState({
               token: storedAccessToken,
               authenticated: true,
               role: parsedUser.role,
               user: parsedUser,
               refreshToken: storedRefreshToken,
            });
         }
      };
      loadAuthData();
   }, []);

   const login = async (access: string, refresh: string, userData: User) => {
      await AsyncStorage.setItem("accessToken", access);
      await AsyncStorage.setItem("refreshToken", refresh);
      await AsyncStorage.setItem("user", JSON.stringify(userData));

      setAuthState({
         token: access,
         authenticated: true,
         role: userData.role,
         user: userData,
         refreshToken: refresh,
      });
   };

   const logout = async () => {
      await AsyncStorage.removeItem("accessToken");
      await AsyncStorage.removeItem("refreshToken");
      await AsyncStorage.removeItem("user");

      setAuthState({
         token: null,
         authenticated: false,
         role: null,
         user: null,
         refreshToken: null,
      });
   };

   const refresh = async () => {
      const refreshToken = await AsyncStorage.getItem("refreshToken");

      if (refreshToken) {
         console.log(refreshToken);

         try {
            const response = await axios.post(`${baseUrl}/v1/users/refresh`, {
               refresh_token: refreshToken,
            });

            const { access_token, refresh_token } = response.data.tokens;
            await AsyncStorage.setItem("accessToken", access_token);
            await AsyncStorage.setItem("refreshToken", refresh_token);

            setAuthState((prevState) => ({
               ...prevState,
               token: access_token,
               refreshToken: refresh_token,
            }));
         } catch (error) {
            console.error("Ошибка обновления токена:", error);
            await logout();
         }
      } else {
         // console.error("Рефреш токен отсутствует.");
         // await logout();
      }
   };

   // Настройка интерцепторов после загрузки токенов
   useEffect(() => {
      setupInterceptors(logout); // Настраиваем интерцепторы, передавая функцию logout
   }, []);

   return <AuthContext.Provider value={{ authState, login, logout, refresh }}>{children}</AuthContext.Provider>;
};
