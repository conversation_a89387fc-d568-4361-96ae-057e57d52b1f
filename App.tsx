import React, { useCallback, useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import Welcome from "./screens/Welcome";
import KnowledgeLevel from "./screens/Knowledge/KnowledgeLevel";
import Promo from "./screens/Promo";
import { Provider, useSelector } from "react-redux";
import store from "./store/store";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import Practice from "./screens/Practice/Practice";
import Achievements from "./screens/Achievements/Achievements";
import Home from "./screens/Home/Home";
import HomeSvg from "./assets/image/nav-svg/HomeSvg";
import PracticeSvg from "./assets/image/nav-svg/PracticeSvg";
import AchievementsSvg from "./assets/image/nav-svg/AchievementsSvg";
import ProfileSvg from "./assets/image/nav-svg/ProfileSvg";
import History from "./screens/Practice/History/History";
import Listening from "./screens/Practice/Listening/Listening";
import Lesson from "./screens/Lesson/Lesson";
import LessonResult from "./screens/Lesson/LessonResult";
import Theory from "./screens/Theory/Theory";
import Profile from "./screens/Profile/Profile";
import ProfileEdit from "./screens/Profile/ProfileEdit";
import Crossword from "./screens/Practice/Crossword/Crossword";
import CrosswordCorrectPage from "./screens/Practice/Crossword/CrosswordCorrectPage";
import { Text, TouchableOpacity } from "react-native";
import * as SplashScreen from "expo-splash-screen";
import { View } from "react-native";
import { AuthProvider, useAuth } from "./context/AuthContext";
import ClassicAuth from "./screens/Auth/ClassicAuth";

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

function AuthStack() {
   return (
      <Stack.Navigator screenOptions={{ navigationBarColor: "#fff" }}>
         <Stack.Screen name="Welcome" component={Welcome} options={{ headerShown: false }} />
         <Stack.Screen name="Promo" component={Promo} options={{ headerShown: false }} />
         <Stack.Screen name="KnowledgeLevel" component={KnowledgeLevel} options={{ headerShown: false }} />
         <Stack.Screen name="ClassicAuth" component={ClassicAuth} options={{ headerShown: false }} />
      </Stack.Navigator>
   );
}

function AppNavigator() {
   const { authState } = useAuth();

   if (!authState.authenticated) {
      return <AuthStack />;
   }

   return (
      <Stack.Navigator screenOptions={{ navigationBarColor: "#fff" }}>
         {/* Стэк навигации для экранов вне табов */}
         <Stack.Screen name="MainTabs" component={TabNavigator} options={{ headerShown: false }} />
         <Stack.Screen name="History" component={History} options={{ headerShown: false }} />
         <Stack.Screen name="Listening" component={Listening} options={{ headerShown: false }} />
         <Stack.Screen name="Lesson" component={Lesson} options={{ headerShown: false, gestureEnabled: false }} />
         <Stack.Screen name="Lesson-Result" component={LessonResult} options={{ headerShown: false }} />
         <Stack.Screen name="Theory" component={Theory} options={{ headerShown: false }} />
         <Stack.Screen name="Promo" component={Promo} options={{ headerShown: false }} />
         <Stack.Screen name="ProfileEdit" component={ProfileEdit} options={{ headerShown: false }} />
         <Stack.Screen name="CrosswordCorrectPage" component={CrosswordCorrectPage} options={{ headerShown: false }} />
         <Stack.Screen name="Crossword" component={Crossword} options={{ headerShown: false }} />
      </Stack.Navigator>
   );
}

function TabNavigator() {
   return (
      <Tab.Navigator
         screenOptions={({ route }) => ({
            tabBarButton: (props) => {
               const { accessibilityState, onPress, children } = props;
               const focused = accessibilityState?.selected;

               return (
                  <TouchableOpacity
                     onPress={onPress}
                     activeOpacity={1}
                     style={{
                        flex: 1,
                        // backgroundColor: focused ? "#F1C644" : "transparent", // Highlighted background
                        justifyContent: "center",
                        alignItems: "center",
                     }}
                  >
                     {children}
                  </TouchableOpacity>
               );
            },
            tabBarIcon: ({ color, size, focused }) => {
               if (route.name === "Home") {
                  return <HomeSvg color={focused ? "#bababa" : "#fff"} />;
               }
               if (route.name === "Practice") {
                  return <PracticeSvg color={focused ? "#bababa" : "#fff"} />;
               }
               if (route.name === "Achievements") {
                  return <AchievementsSvg color={focused ? "#bababa" : "#fff"} />;
               }
               if (route.name === "Profile") {
                  return <ProfileSvg color={focused ? "#bababa" : "#fff"} />;
               }
            },
            tabBarLabel: () => null,
            tabBarStyle: {
               display: "flex",
               height: 80,
            },
         })}
      >
         <Tab.Screen name="Home" component={Home} options={{ headerShown: false }} />
         <Tab.Screen name="Practice" component={Practice} options={{ headerShown: false }} />
         <Tab.Screen name="Achievements" component={Achievements} options={{ headerShown: false }} />
         <Tab.Screen name="Profile" component={Profile} options={{ headerShown: false }} />
      </Tab.Navigator>
   );
}

SplashScreen.preventAutoHideAsync();

export default function App() {
   const [appIsReady, setAppIsReady] = useState(false);

   useEffect(() => {
      async function prepare() {
         try {
            // Artificially delay for two seconds to simulate a slow loading
            // experience. Please remove this if you copy and paste the code!
            await new Promise((resolve) => setTimeout(resolve, 2000));
         } catch (e) {
            console.warn(e);
         } finally {
            // Tell the application to render
            setAppIsReady(true);
         }
      }

      prepare();
   }, []);

   const onLayoutRootView = useCallback(async () => {
      if (appIsReady) {
         // This tells the splash screen to hide immediately! If we call this after
         // `setAppIsReady`, then we may see a blank screen while the app is
         // loading its initial state and rendering its first pixels. So instead,
         // we hide the splash screen once we know the root view has already
         // performed layout.
         await SplashScreen.hideAsync();
      }
   }, [appIsReady]);

   if (!appIsReady) {
      return null;
   }

   return (
      <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
         <Provider store={store}>
            <AuthProvider>
               <NavigationContainer>
                  <AppNavigator />
               </NavigationContainer>
            </AuthProvider>
         </Provider>
      </View>
   );
}
