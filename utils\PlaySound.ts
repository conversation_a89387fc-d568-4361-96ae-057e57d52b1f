import { AudioPlayer, AudioSource } from "expo-audio";
import { CorrectAudio, WrongAudio } from "../assets/audio";

export const PlayWrongSound = async () => {
    const player = new AudioPlayer(WrongAudio as AudioSource);
    await player.play();

    const subscription = player.addListener('playbackStatusUpdate', (status) => {
       if (status.didJustFinish) {
          player.remove();
          subscription?.remove();
       }
    });
 };

export const PlayCorrectSound = async () => {
    const player = new AudioPlayer(CorrectAudio as AudioSource);
    await player.play();

    const subscription = player.addListener('playbackStatusUpdate', (status) => {
       if (status.didJustFinish) {
          player.remove();
          subscription?.remove();
       }
    });
 };

 