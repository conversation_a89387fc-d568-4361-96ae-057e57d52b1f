import React, { useEffect, useRef, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { useAudioPlayer, AudioSource } from "expo-audio";

interface AudioPlayerProps {
   url: string;
   autoPlay?: boolean;
   onPlay?: () => void;
   onPause?: () => void;
   onStop?: () => void;
   onEnd?: () => void;
   onError?: (error: Error) => void;
}

export interface AudioPlayerRef {
   play: () => Promise<void>;
   pause: () => Promise<void>;
   stop: () => Promise<void>;
}

export const AudioPlayer = forwardRef<AudioPlayerRef, AudioPlayerProps>(
   ({ url, autoPlay = false, onPlay, onPause, onStop, onEnd, onError }, ref) => {
      const player = useAudioPlayer(url as AudioSource);

      useEffect(() => {
         const subscription = player.addListener('playbackStatusUpdate', (status) => {
            if (status.didJustFinish) {
               onEnd?.();
            }
         });

         if (autoPlay) {
            player.play().then(() => onPlay?.()).catch(onError);
         }

         return () => {
            subscription?.remove();
         };
      }, [url, autoPlay, onPlay, onEnd, onError]);

      const play = async () => {
         try {
            if (player.currentTime === player.duration) {
               player.seekTo(0);
            }
            await player.play();
            onPlay?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      const pause = async () => {
         try {
            player.pause();
            onPause?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      const stop = async () => {
         try {
            player.pause();
            player.seekTo(0);
            onStop?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      useImperativeHandle(ref, () => ({
         play,
         pause,
         stop,
      }));

      return null;
   }
);
