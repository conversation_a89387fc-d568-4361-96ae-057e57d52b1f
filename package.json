{"name": "kazakh-lingo-mobile", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^4.6.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.7", "expo": "53", "expo-av": "^15.1.7", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "jwt-decode": "^4.0.0", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sound": "^0.11.2", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.1.0", "react-navigation": "^5.0.0", "react-redux": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "^5.1.3"}, "private": true}