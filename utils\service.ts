// axiosInstance.ts
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { jwtDecode, JwtPayload } from "jwt-decode";

// Конфигурация сервера
const prod = "https://narxoz-sporthub.online";
const local = "http://172.31.22.100:8080";

export const baseUrl = local; // Локальный или продакшн URL

const axiosInstance = axios.create({
   baseURL: baseUrl,
   headers: {
      "Content-Type": "application/json",
   },
});

// Функция проверки истечения токена
const isTokenExpired = (token: string): boolean => {
   try {
      const { exp } = jwtDecode<JwtPayload>(token);
      return exp ? Date.now() >= exp * 1000 : true;
   } catch (error) {
      console.error("Ошибка декодирования токена:", error);
      return true;
   }
};

// Функция настройки интерцепторов
let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

export const setupInterceptors = (logout: () => Promise<void>) => {
   axiosInstance.interceptors.request.use(
      async (config) => {
         const accessToken = await AsyncStorage.getItem("accessToken");

         if (accessToken && !isTokenExpired(accessToken)) {
            config.headers.Authorization = `Bearer ${accessToken}`;
         }
         return config;
      },
      (error) => Promise.reject(error)
   );

   axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
         const originalRequest = error.config;

         if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            // Если обновление токена уже выполняется, ждем его завершения
            if (isRefreshing) {
               console.log("Ожидаем завершения обновления токена...");
               try {
                  await refreshPromise;
                  return axiosInstance(originalRequest);
               } catch (refreshError) {
                  return Promise.reject(refreshError);
               }
            }

            isRefreshing = true;
            refreshPromise = (async () => {
               const refreshToken = await AsyncStorage.getItem("refreshToken");
               console.log("Отправленный рефреш токен:", refreshToken);

               if (refreshToken && !isTokenExpired(refreshToken)) {
                  try {
                     const { data } = await axios.post(`${baseUrl}/v1/users/refresh`, {
                        refresh_token: refreshToken,
                     });

                     await AsyncStorage.setItem("accessToken", data.tokens.access_token);
                     await AsyncStorage.setItem("refreshToken", data.tokens.refresh_token);

                     console.log("ЗАПИСАННЫЙ рефреш токен:", data.tokens.refresh_token);
                     axiosInstance.defaults.headers.Authorization = `Bearer ${data.tokens.access_token}`;

                     return data;
                  } catch (refreshError) {
                     console.error("Ошибка обновления токена:", refreshError);
                     await logout();
                     throw refreshError;
                  } finally {
                     isRefreshing = false;
                     refreshPromise = null;
                  }
               } else {
                  isRefreshing = false;
                  await logout();
                  throw new Error("Refresh token expired or missing.");
               }
            })();

            try {
               await refreshPromise;
               return axiosInstance(originalRequest);
            } catch (refreshError) {
               return Promise.reject(refreshError);
            }
         }

         return Promise.reject(error);
      }
   );
};

export const PostRequest = async (url: string, body: any) => {
   try {
      const response = await axiosInstance.post(url, body);
      return response.data;
   } catch (error) {
      throw error;
   }
};

export const PutRequest = async (url: string, body: any) => {
   try {
      const response = await axiosInstance.put(url, body);
      return response.data;
   } catch (error) {
      throw error;
   }
};

export const GetRequest = async (url: string) => {
   try {
      const response = await axiosInstance.get(url);
      return response.data;
   } catch (error) {
      throw error;
   }
};

export default axiosInstance;
